<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [ElectronicController_Board\ElectronicController_Board.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ElectronicController_Board\ElectronicController_Board.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 21:33:30 2025
<BR><P>
<H3>Maximum Stack Usage =       1160 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
user_task &rArr; process_command &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1d]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1d]">ADC_IRQHandler</a><BR>
 <LI><a href="#[5]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">BusFault_Handler</a><BR>
 <LI><a href="#[3]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">HardFault_Handler</a><BR>
 <LI><a href="#[4]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">MemManage_Handler</a><BR>
 <LI><a href="#[2]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">NMI_Handler</a><BR>
 <LI><a href="#[6]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1d]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[20]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[21]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1e]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">CAN2_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4c]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[59]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream0_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream1_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">EXTI15_10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[22]">EXTI9_5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[f]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5b]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2d]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2c]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">OTG_FS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[58]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[c]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[10]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2f]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3e]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">TIM1_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[27]">TIM2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">TIM3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[29]">TIM4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">TIM5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">TIM6_DAC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">TIM7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[39]">TIM8_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">UART5_IRQHandler</a> from stm32f4xx_it.o(i.UART5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[66]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[69]">UART_DMAError</a> from stm32f4xx_hal_uart.o(i.UART_DMAError) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[67]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[68]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[30]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from stm32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">USART3_IRQHandler</a> from stm32f4xx_it.o(i.USART3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">USART6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[b]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5e]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[60]">_sbackspace</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[61]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[5f]">_sgetc</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[6b]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[6a]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[6c]">app_pid_report_task</a> from app_pid.o(i.app_pid_report_task) referenced from app_pid.o(i.app_pid_init)
 <LI><a href="#[6c]">app_pid_report_task</a> from app_pid.o(i.app_pid_report_task) referenced from app_pid.o(i.app_pid_parse_cmd)
 <LI><a href="#[6c]">app_pid_report_task</a> from app_pid.o(i.app_pid_report_task) referenced from app_pid.o(i.app_pid_report_task)
 <LI><a href="#[6c]">app_pid_report_task</a> from app_pid.o(i.app_pid_report_task) referenced from app_pid.o(i.app_pid_start)
 <LI><a href="#[6d]">app_pid_task</a> from app_pid.o(i.app_pid_task) referenced from app_pid.o(i.app_pid_parse_cmd)
 <LI><a href="#[6d]">app_pid_task</a> from app_pid.o(i.app_pid_task) referenced from app_pid.o(i.app_pid_start)
 <LI><a href="#[6d]">app_pid_task</a> from app_pid.o(i.app_pid_task) referenced from app_pid.o(i.app_pid_task)
 <LI><a href="#[6f]">bsp_get_systick</a> from main.o(i.bsp_get_systick) referenced from main.o(i.main)
 <LI><a href="#[71]">default_laser_callback</a> from app_maixcam.o(i.default_laser_callback) referenced from app_maixcam.o(.data)
 <LI><a href="#[71]">default_laser_callback</a> from app_maixcam.o(i.default_laser_callback) referenced from app_maixcam.o(i.maixcam_set_callback)
 <LI><a href="#[62]">isspace</a> from isspace_c.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[5c]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[65]">maixcam_task</a> from app_maixcam.o(i.maixcam_task) referenced from usart.o(i.HAL_UARTEx_RxEventCallback)
 <LI><a href="#[70]">oled_task</a> from main.o(i.oled_task) referenced from main.o(i.main)
 <LI><a href="#[70]">oled_task</a> from main.o(i.oled_task) referenced from main.o(i.oled_task)
 <LI><a href="#[6e]">pid_laser_coord_callback</a> from app_pid.o(i.pid_laser_coord_callback) referenced from app_pid.o(i.app_pid_parse_cmd)
 <LI><a href="#[6e]">pid_laser_coord_callback</a> from app_pid.o(i.pid_laser_coord_callback) referenced from app_pid.o(i.app_pid_start)
 <LI><a href="#[7f]">ssd1306_interface_debug_print</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_debug_print) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[7e]">ssd1306_interface_delay_ms</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_delay_ms) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[73]">ssd1306_interface_iic_deinit</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_deinit) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[72]">ssd1306_interface_iic_init</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_init) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[74]">ssd1306_interface_iic_write</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_write) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[7c]">ssd1306_interface_reset_gpio_deinit</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_deinit) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[7b]">ssd1306_interface_reset_gpio_init</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_init) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[7d]">ssd1306_interface_reset_gpio_write</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_write) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[79]">ssd1306_interface_spi_cmd_data_gpio_deinit</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_deinit) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[78]">ssd1306_interface_spi_cmd_data_gpio_init</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_init) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[7a]">ssd1306_interface_spi_cmd_data_gpio_write</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_write) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[76]">ssd1306_interface_spi_deinit</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_deinit) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[75]">ssd1306_interface_spi_init</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_init) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[77]">ssd1306_interface_spi_write_cmd</a> from stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_write_cmd) referenced from app_oled.o(i.ssd1306_basic_init)
 <LI><a href="#[63]">usart_task</a> from app_uasrt.o(i.usart_task) referenced from usart.o(i.HAL_UARTEx_RxEventCallback)
 <LI><a href="#[64]">user_task</a> from app_uasrt.o(i.user_task) referenced from usart.o(i.HAL_UARTEx_RxEventCallback)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[5e]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[13f]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[80]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[96]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[140]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[141]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[142]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[143]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[144]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[145]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[116]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[146]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[147]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[86]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[148]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[149]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[85]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_task
</UL>

<P><STRONG><a name="[9c]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Parse_Response
</UL>

<P><STRONG><a name="[14a]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[87]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[110]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[f4]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_task
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_parse_data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[88]"></a>__0sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_task
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_parse_data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[8a]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[14b]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[e9]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[84]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[14c]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[14d]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[89]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[5f]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[60]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[14e]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[91]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[92]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[93]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[94]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[95]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[e6]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[81]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[14f]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[8e]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[150]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[62]"></a>isspace</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, isspace_c.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ctype_lookup
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[8c]"></a>__vfscanf</STRONG> (Thumb, 810 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[90]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[8f]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[97]"></a>__ctype_lookup</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, ctype_c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[99]"></a>Emm_V5_En_Control</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_En_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_En_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
</UL>

<P><STRONG><a name="[9b]"></a>Emm_V5_Parse_Response</STRONG> (Thumb, 646 bytes, Stack size 16 bytes, emm_v5.o(i.Emm_V5_Parse_Response))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Emm_V5_Parse_Response
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
</UL>

<P><STRONG><a name="[9d]"></a>Emm_V5_Pos_Control</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, emm_v5.o(i.Emm_V5_Pos_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Emm_V5_Pos_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_reset_command
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[9e]"></a>Emm_V5_Read_Sys_Params</STRONG> (Thumb, 218 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_Read_Sys_Params))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_Read_Sys_Params &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
</UL>

<P><STRONG><a name="[9f]"></a>Emm_V5_Reset_CurPos_To_Zero</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_Reset_CurPos_To_Zero &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a0]"></a>Emm_V5_Stop_Now</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_Stop_Now))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_Stop_Now &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Stop
</UL>

<P><STRONG><a name="[a1]"></a>Emm_V5_Vel_Control</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, emm_v5.o(i.Emm_V5_Vel_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Emm_V5_Vel_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set_Speed
</UL>

<P><STRONG><a name="[ca]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_DMA_Abort</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[c4]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[98]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 470 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream1_IRQHandler
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[a4]"></a>HAL_DMA_Init</STRONG> (Thumb, 334 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[e1]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 146 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[a5]"></a>HAL_Delay</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[b3]"></a>HAL_GPIO_DeInit</STRONG> (Thumb, 324 bytes, Stack size 36 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_deinit
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_deinit
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_clock_deinit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_deinit
</UL>

<P><STRONG><a name="[b4]"></a>HAL_GPIO_Init</STRONG> (Thumb, 494 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_clock_init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_init
</UL>

<P><STRONG><a name="[cf]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_cmd
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_clock_write
</UL>

<P><STRONG><a name="[a3]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[db]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[a6]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a8]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[a9]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 4 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[cb]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[ab]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 96 bytes, Stack size 4 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[a7]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[ac]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 324 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[df]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[e0]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[ad]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[ae]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 990 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[af]"></a>HAL_SPI_DeInit</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_SPI_DeInit &rArr; HAL_SPI_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_deinit
</UL>

<P><STRONG><a name="[b1]"></a>HAL_SPI_Init</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[b0]"></a>HAL_SPI_MspDeInit</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_msp.o(i.HAL_SPI_MspDeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_SPI_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DeInit
</UL>

<P><STRONG><a name="[b2]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, stm32f4xx_hal_msp.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[b5]"></a>HAL_SPI_Transmit</STRONG> (Thumb, 416 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SPI_Transmit &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_cmd
</UL>

<P><STRONG><a name="[ff]"></a>HAL_SYSTICK_CLKSourceConfig</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[aa]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[b7]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
</UL>

<P><STRONG><a name="[b8]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[d1]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 366 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_TIM_ConfigClockSource
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
</UL>

<P><STRONG><a name="[d2]"></a>HAL_TIM_PWM_ConfigChannel</STRONG> (Thumb, 580 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIM_PWM_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
</UL>

<P><STRONG><a name="[ba]"></a>HAL_TIM_PWM_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_PWM_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_MspInit
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
</UL>

<P><STRONG><a name="[bb]"></a>HAL_TIM_PWM_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
</UL>

<P><STRONG><a name="[bc]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[be]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 382 bytes, Stack size 32 bytes, usart.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[c5]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[c2]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 772 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[c7]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
</UL>

<P><STRONG><a name="[c8]"></a>HAL_UART_MspInit</STRONG> (Thumb, 580 bytes, Stack size 64 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[dd]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[de]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[9a]"></a>HAL_UART_Transmit</STRONG> (Thumb, 338 bytes, Stack size 40 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Reset_CurPos_To_Zero
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_reset_command
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Vel_Control
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Read_Sys_Params
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Pos_Control
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
</UL>

<P><STRONG><a name="[c6]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[cd]"></a>MX_DMA_Init</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ce]"></a>MX_GPIO_Init</STRONG> (Thumb, 216 bytes, Stack size 48 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[104]"></a>MX_SPI1_Init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, spi.o(i.MX_SPI1_Init))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d0]"></a>MX_TIM9_Init</STRONG> (Thumb, 192 bytes, Stack size 88 bytes, tim.o(i.MX_TIM9_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_TIM9_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d3]"></a>MX_UART5_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart.o(i.MX_UART5_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_UART5_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d4]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d5]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d6]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart.o(i.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[d7]"></a>Motor_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, app_motor.o(i.Motor_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Motor_Init &rArr; Emm_V5_Stop_Now &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d8]"></a>Motor_Set_Speed</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, app_motor.o(i.Motor_Set_Speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Motor_Set_Speed &rArr; Emm_V5_Vel_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Vel_Control
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_calc
</UL>

<P><STRONG><a name="[d9]"></a>Motor_Stop</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_motor.o(i.Motor_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Motor_Stop &rArr; Emm_V5_Stop_Now &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_calc
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[dc]"></a>SystemClock_Config</STRONG> (Thumb, 140 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5d]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[b9]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[40]"></a>UART5_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.UART5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = UART5_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[bd]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART3_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = USART3_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e2]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[151]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[10f]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[152]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[153]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[e4]"></a>__0vsnprintf</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[154]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[155]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[156]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[10e]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_reset_command
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[157]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[158]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[159]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[ef]"></a>app_pid_calc</STRONG> (Thumb, 324 bytes, Stack size 24 bytes, app_pid.o(i.app_pid_calc))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = app_pid_calc &rArr; Motor_Set_Speed &rArr; Emm_V5_Vel_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Stop
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set_Speed
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_positional
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_task
</UL>

<P><STRONG><a name="[f1]"></a>app_pid_init</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, app_pid.o(i.app_pid_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = app_pid_init &rArr; multiTimerStart
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_init
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_laser_callback
</UL>

<P><STRONG><a name="[f3]"></a>app_pid_parse_cmd</STRONG> (Thumb, 518 bytes, Stack size 64 bytes, app_pid.o(i.app_pid_parse_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = app_pid_parse_cmd &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_set_callback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Stop
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_target
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_params
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_limit
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_reset
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStop
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>

<P><STRONG><a name="[fb]"></a>app_pid_report</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, app_pid.o(i.app_pid_report), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[6c]"></a>app_pid_report_task</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, app_pid.o(i.app_pid_report_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = app_pid_report_task &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
</UL>
<BR>[Address Reference Count : 4]<UL><LI> app_pid.o(i.app_pid_parse_cmd)
<LI> app_pid.o(i.app_pid_report_task)
<LI> app_pid.o(i.app_pid_start)
<LI> app_pid.o(i.app_pid_init)
</UL>
<P><STRONG><a name="[fc]"></a>app_pid_set_target</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, app_pid.o(i.app_pid_set_target))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_pid_set_target
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_target
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_laser_callback
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[fd]"></a>app_pid_start</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, app_pid.o(i.app_pid_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = app_pid_start &rArr; multiTimerStart
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_set_callback
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_laser_callback
</UL>

<P><STRONG><a name="[6d]"></a>app_pid_task</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, app_pid.o(i.app_pid_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = app_pid_task &rArr; app_pid_calc &rArr; Motor_Set_Speed &rArr; Emm_V5_Vel_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_calc
</UL>
<BR>[Address Reference Count : 3]<UL><LI> app_pid.o(i.app_pid_parse_cmd)
<LI> app_pid.o(i.app_pid_start)
<LI> app_pid.o(i.app_pid_task)
</UL>
<P><STRONG><a name="[6f]"></a>bsp_get_systick</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, main.o(i.bsp_get_systick))
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[fe]"></a>delay_init</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, delay.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[100]"></a>delay_ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_ms &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_delay_ms
</UL>

<P><STRONG><a name="[eb]"></a>delay_us</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_wait_ack
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_stop
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_start
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_send_byte
</UL>

<P><STRONG><a name="[101]"></a>iic_deinit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, iic.o(i.iic_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = iic_deinit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_iic_deinit
</UL>

<P><STRONG><a name="[102]"></a>iic_init</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, iic.o(i.iic_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = iic_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_iic_init
</UL>

<P><STRONG><a name="[103]"></a>iic_write</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, iic.o(i.iic_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = iic_write &rArr; a_iic_wait_ack &rArr; a_iic_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_wait_ack
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_stop
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_start
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_iic_write
</UL>

<P><STRONG><a name="[5c]"></a>main</STRONG> (Thumb, 158 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = main &rArr; save_initial_position &rArr; Emm_V5_Read_Sys_Params &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_clear
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerYield
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerInstall
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM9_Init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Reset_CurPos_To_Zero
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[10b]"></a>maixcam_parse_data</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, app_maixcam.o(i.maixcam_parse_data), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
</UL>

<P><STRONG><a name="[f8]"></a>maixcam_set_callback</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, app_maixcam.o(i.maixcam_set_callback))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_start
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[65]"></a>maixcam_task</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, app_maixcam.o(i.maixcam_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = maixcam_task &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart.o(i.HAL_UARTEx_RxEventCallback)
</UL>
<P><STRONG><a name="[105]"></a>multiTimerInstall</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, multitimer.o(i.multiTimerInstall))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c0]"></a>multiTimerStart</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, multitimer.o(i.multiTimerStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = multiTimerStart
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_task
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_report_task
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_start
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[fa]"></a>multiTimerStop</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, multitimer.o(i.multiTimerStop))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[10a]"></a>multiTimerYield</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, multitimer.o(i.multiTimerYield))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = multiTimerYield
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c1]"></a>my_printf</STRONG> (Thumb, 50 bytes, Stack size 544 bytes, app_uasrt.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_report_task
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_report
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_laser_callback
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_reset_command
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
</UL>

<P><STRONG><a name="[70]"></a>oled_task</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, main.o(i.oled_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = oled_task &rArr; ssd1306_basic_string &rArr; ssd1306_gram_write_string
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_string
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_clear
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiTimerStart
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Address Reference Count : 2]<UL><LI> main.o(i.main)
<LI> main.o(i.oled_task)
</UL>
<P><STRONG><a name="[112]"></a>parse_x_motor_data</STRONG> (Thumb, 190 bytes, Stack size 8 bytes, app_uasrt.o(i.parse_x_motor_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = parse_x_motor_data &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
</UL>

<P><STRONG><a name="[113]"></a>parse_y_motor_data</STRONG> (Thumb, 190 bytes, Stack size 8 bytes, app_uasrt.o(i.parse_y_motor_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = parse_y_motor_data &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
</UL>

<P><STRONG><a name="[f0]"></a>pid_calculate_positional</STRONG> (Thumb, 136 bytes, Stack size 0 bytes, pid.o(i.pid_calculate_positional))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_calc
</UL>

<P><STRONG><a name="[f2]"></a>pid_init</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, pid.o(i.pid_init))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_init
</UL>

<P><STRONG><a name="[6e]"></a>pid_laser_coord_callback</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, app_pid.o(i.pid_laser_coord_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pid_laser_coord_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_target
</UL>
<BR>[Address Reference Count : 2]<UL><LI> app_pid.o(i.app_pid_parse_cmd)
<LI> app_pid.o(i.app_pid_start)
</UL>
<P><STRONG><a name="[f9]"></a>pid_reset</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, pid.o(i.pid_reset))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_start
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[f7]"></a>pid_set_limit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, pid.o(i.pid_set_limit))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[f6]"></a>pid_set_params</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, pid.o(i.pid_set_params))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[f5]"></a>pid_set_target</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, pid.o(i.pid_set_target))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_laser_coord_callback
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_set_target
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
</UL>

<P><STRONG><a name="[114]"></a>process_command</STRONG> (Thumb, 212 bytes, Stack size 544 bytes, app_uasrt.o(i.process_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1152<LI>Call Chain = process_command &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_set_target
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Pos_Control
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>

<P><STRONG><a name="[115]"></a>process_reset_command</STRONG> (Thumb, 112 bytes, Stack size 536 bytes, app_uasrt.o(i.process_reset_command), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Pos_Control
</UL>

<P><STRONG><a name="[10c]"></a>rt_ringbuffer_data_len</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, ringbuffer.o(i.rt_ringbuffer_data_len))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_task
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[10d]"></a>rt_ringbuffer_get</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_ringbuffer_get
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_task
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_task
</UL>

<P><STRONG><a name="[106]"></a>rt_ringbuffer_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, ringbuffer.o(i.rt_ringbuffer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_ringbuffer_init
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bf]"></a>rt_ringbuffer_put</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_ringbuffer_put
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[109]"></a>save_initial_position</STRONG> (Thumb, 72 bytes, Stack size 528 bytes, app_uasrt.o(i.save_initial_position))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = save_initial_position &rArr; Emm_V5_Read_Sys_Params &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Read_Sys_Params
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[117]"></a>spi_deinit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, spi.o(i.spi_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = spi_deinit &rArr; HAL_SPI_DeInit &rArr; HAL_SPI_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DeInit
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_spi_deinit
</UL>

<P><STRONG><a name="[118]"></a>spi_init</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, spi.o(i.spi_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = spi_init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_spi_init
</UL>

<P><STRONG><a name="[119]"></a>spi_write_cmd</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, spi.o(i.spi_write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = spi_write_cmd &rArr; HAL_SPI_Transmit &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_spi_write_cmd
</UL>

<P><STRONG><a name="[108]"></a>ssd1306_basic_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, app_oled.o(i.ssd1306_basic_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ssd1306_basic_clear &rArr; ssd1306_clear &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[107]"></a>ssd1306_basic_init</STRONG> (Thumb, 1714 bytes, Stack size 16 bytes, app_oled.o(i.ssd1306_basic_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ssd1306_basic_init &rArr; ssd1306_clear &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_debug_print
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_zoom_in
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_segment_remap
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_scan_direction
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_precharge_period
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_page_address_range
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_multiplex_ratio
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_memory_addressing_mode
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_low_column_start_address
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_interface
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_high_column_start_address
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_fade_blinking_mode
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_entire_display
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_start_line
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_offset
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_mode
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_clock
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_deselect_level
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_contrast
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_com_pins_hardware_conf
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_column_address_range
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_charge_pump
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_addr_pin
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_deinit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_deactivate_scroll
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[111]"></a>ssd1306_basic_string</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, app_oled.o(i.ssd1306_basic_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ssd1306_basic_string &rArr; ssd1306_gram_write_string
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_gram_write_string
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_gram_update
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[11a]"></a>ssd1306_clear</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, driver_ssd1306.o(i.ssd1306_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ssd1306_clear &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_clear
</UL>

<P><STRONG><a name="[126]"></a>ssd1306_deactivate_scroll</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_deactivate_scroll))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_deactivate_scroll &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[120]"></a>ssd1306_deinit</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, driver_ssd1306.o(i.ssd1306_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ssd1306_deinit &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[136]"></a>ssd1306_gram_update</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, driver_ssd1306.o(i.ssd1306_gram_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ssd1306_gram_update &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_string
</UL>

<P><STRONG><a name="[135]"></a>ssd1306_gram_write_string</STRONG> (Thumb, 448 bytes, Stack size 80 bytes, driver_ssd1306.o(i.ssd1306_gram_write_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ssd1306_gram_write_string
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_string
</UL>

<P><STRONG><a name="[11d]"></a>ssd1306_init</STRONG> (Thumb, 372 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ssd1306_init
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[7f]"></a>ssd1306_interface_debug_print</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_debug_print))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[7e]"></a>ssd1306_interface_delay_ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ssd1306_interface_delay_ms &rArr; delay_ms &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[73]"></a>ssd1306_interface_iic_deinit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ssd1306_interface_iic_deinit &rArr; iic_deinit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_deinit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[72]"></a>ssd1306_interface_iic_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ssd1306_interface_iic_init &rArr; iic_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[74]"></a>ssd1306_interface_iic_write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ssd1306_interface_iic_write &rArr; iic_write &rArr; a_iic_wait_ack &rArr; a_iic_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[7c]"></a>ssd1306_interface_reset_gpio_deinit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ssd1306_interface_reset_gpio_deinit &rArr; wire_clock_deinit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_clock_deinit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[7b]"></a>ssd1306_interface_reset_gpio_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ssd1306_interface_reset_gpio_init &rArr; wire_clock_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_clock_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[7d]"></a>ssd1306_interface_reset_gpio_write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ssd1306_interface_reset_gpio_write &rArr; wire_clock_write
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_clock_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[79]"></a>ssd1306_interface_spi_cmd_data_gpio_deinit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ssd1306_interface_spi_cmd_data_gpio_deinit &rArr; wire_deinit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_deinit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[78]"></a>ssd1306_interface_spi_cmd_data_gpio_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ssd1306_interface_spi_cmd_data_gpio_init &rArr; wire_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[7a]"></a>ssd1306_interface_spi_cmd_data_gpio_write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_write))
<BR><BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wire_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[76]"></a>ssd1306_interface_spi_deinit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = ssd1306_interface_spi_deinit &rArr; spi_deinit &rArr; HAL_SPI_DeInit &rArr; HAL_SPI_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_deinit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[75]"></a>ssd1306_interface_spi_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ssd1306_interface_spi_init &rArr; spi_init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[77]"></a>ssd1306_interface_spi_write_cmd</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = ssd1306_interface_spi_write_cmd &rArr; spi_write_cmd &rArr; HAL_SPI_Transmit &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_cmd
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_oled.o(i.ssd1306_basic_init)
</UL>
<P><STRONG><a name="[11c]"></a>ssd1306_set_addr_pin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_addr_pin))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[133]"></a>ssd1306_set_charge_pump</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_charge_pump))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_charge_pump &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[11f]"></a>ssd1306_set_column_address_range</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_column_address_range))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_column_address_range &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[130]"></a>ssd1306_set_com_pins_hardware_conf</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_com_pins_hardware_conf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_com_pins_hardware_conf &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[128]"></a>ssd1306_set_contrast</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_contrast))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_contrast &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[131]"></a>ssd1306_set_deselect_level</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_deselect_level))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_deselect_level &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[11e]"></a>ssd1306_set_display</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_display))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_display &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[12e]"></a>ssd1306_set_display_clock</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_display_clock))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_display_clock &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[12b]"></a>ssd1306_set_display_mode</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_display_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_display_mode &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[12d]"></a>ssd1306_set_display_offset</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_display_offset))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_display_offset &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[124]"></a>ssd1306_set_display_start_line</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_display_start_line))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ssd1306_set_display_start_line &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[134]"></a>ssd1306_set_entire_display</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_entire_display))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_entire_display &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[125]"></a>ssd1306_set_fade_blinking_mode</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_fade_blinking_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_fade_blinking_mode &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[123]"></a>ssd1306_set_high_column_start_address</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_high_column_start_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ssd1306_set_high_column_start_address &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[11b]"></a>ssd1306_set_interface</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_interface))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[122]"></a>ssd1306_set_low_column_start_address</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_low_column_start_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ssd1306_set_low_column_start_address &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[132]"></a>ssd1306_set_memory_addressing_mode</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_memory_addressing_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_memory_addressing_mode &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[12c]"></a>ssd1306_set_multiplex_ratio</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_multiplex_ratio))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_multiplex_ratio &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[121]"></a>ssd1306_set_page_address_range</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_page_address_range))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_page_address_range &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[12f]"></a>ssd1306_set_precharge_period</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_precharge_period))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_precharge_period &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[12a]"></a>ssd1306_set_scan_direction</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_scan_direction))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_scan_direction &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[129]"></a>ssd1306_set_segment_remap</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, driver_ssd1306.o(i.ssd1306_set_segment_remap))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_segment_remap &rArr; a_ssd1306_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[127]"></a>ssd1306_set_zoom_in</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, driver_ssd1306.o(i.ssd1306_set_zoom_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_set_zoom_in &rArr; a_ssd1306_multiple_write_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_basic_init
</UL>

<P><STRONG><a name="[63]"></a>usart_task</STRONG> (Thumb, 222 bytes, Stack size 216 bytes, app_uasrt.o(i.usart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = usart_task &rArr; parse_y_motor_data &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Parse_Response
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart.o(i.HAL_UARTEx_RxEventCallback)
</UL>
<P><STRONG><a name="[64]"></a>user_task</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, app_uasrt.o(i.user_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 1160<LI>Call Chain = user_task &rArr; process_command &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_parse_cmd
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart.o(i.HAL_UARTEx_RxEventCallback)
</UL>
<P><STRONG><a name="[139]"></a>wire_clock_deinit</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, wire.o(i.wire_clock_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = wire_clock_deinit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_reset_gpio_deinit
</UL>

<P><STRONG><a name="[13a]"></a>wire_clock_init</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, wire.o(i.wire_clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = wire_clock_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_reset_gpio_init
</UL>

<P><STRONG><a name="[13b]"></a>wire_clock_write</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, wire.o(i.wire_clock_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wire_clock_write
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_reset_gpio_write
</UL>

<P><STRONG><a name="[13c]"></a>wire_deinit</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, wire.o(i.wire_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = wire_deinit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_spi_cmd_data_gpio_deinit
</UL>

<P><STRONG><a name="[13d]"></a>wire_init</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, wire.o(i.wire_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = wire_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_spi_cmd_data_gpio_init
</UL>

<P><STRONG><a name="[13e]"></a>wire_write</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, wire.o(i.wire_write))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_interface_spi_cmd_data_gpio_write
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[b6]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
</UL>

<P><STRONG><a name="[da]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 204 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>

<P><STRONG><a name="[66]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[69]"></a>UART_DMAError</STRONG> (Thumb, 174 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[67]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 152 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[68]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[c3]"></a>UART_Receive_IT</STRONG> (Thumb, 206 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[c9]"></a>UART_SetConfig</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[cc]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[ea]"></a>a_iic_send_byte</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, iic.o(i.a_iic_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = a_iic_send_byte &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_write
</UL>

<P><STRONG><a name="[ec]"></a>a_iic_start</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, iic.o(i.a_iic_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = a_iic_start &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_write
</UL>

<P><STRONG><a name="[ed]"></a>a_iic_stop</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, iic.o(i.a_iic_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = a_iic_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_write
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_wait_ack
</UL>

<P><STRONG><a name="[ee]"></a>a_iic_wait_ack</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, iic.o(i.a_iic_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = a_iic_wait_ack &rArr; a_iic_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a_iic_stop
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iic_write
</UL>

<P><STRONG><a name="[138]"></a>a_ssd1306_multiple_write_byte</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, driver_ssd1306.o(i.a_ssd1306_multiple_write_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = a_ssd1306_multiple_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_zoom_in
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_precharge_period
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_page_address_range
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_multiplex_ratio
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_memory_addressing_mode
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_fade_blinking_mode
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_offset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_clock
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_deselect_level
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_contrast
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_com_pins_hardware_conf
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_column_address_range
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_charge_pump
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_deinit
</UL>

<P><STRONG><a name="[137]"></a>a_ssd1306_write_byte</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, driver_ssd1306.o(i.a_ssd1306_write_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = a_ssd1306_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_segment_remap
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_scan_direction
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_low_column_start_address
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_high_column_start_address
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_entire_display
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_start_line
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display_mode
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_display
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_gram_update
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_deinit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_deactivate_scroll
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_clear
</UL>

<P><STRONG><a name="[71]"></a>default_laser_callback</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, app_maixcam.o(i.default_laser_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = default_laser_callback &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_start
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_pid_set_target
</UL>
<BR>[Address Reference Count : 2]<UL><LI> app_maixcam.o(i.maixcam_set_callback)
<LI> app_maixcam.o(.data)
</UL>
<P><STRONG><a name="[e5]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e3]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>

<P><STRONG><a name="[e8]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e7]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6b]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[6a]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[61]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
