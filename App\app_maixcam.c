// Copyright (c) 2024 米醋电子工作室
// 文件名: app_maixcam.c
// 描述: 该文件包含MaixCam数据解析和处理的相关函数。
// 主要功能包括：解析特定格式的MaixCam坐标数据，通过回调函数处理解析结果，
// 并提供一些基于解析数据的判断辅助函数。

#include "app_maixcam.h"  // 包含MaixCam应用层头文件，定义了LaserCoord_t等结构体
#include <string.h>       // 包含字符串处理函数，如strncmp
#include <stdio.h>        // 包含标准输入输出函数，如sscanf, my_printf
#include <math.h>         // 包含数学函数，如sqrtf (用于计算距离)
#include <stdlib.h>       // 包含标准库函数，如abs (用于计算绝对值)

// 假设这些是预定义的宏或枚举，用于标识不同类型的激光数据
// #define RED_LASER_ID  'R' // 或者其他你定义的用于表示“to”数据的ID (目标点)
// #define GREEN_LASER_ID 'G' // 或者其他你定义的用于表示“pur”数据的ID (实际激光点)

// 全局变量，用于存储最新解析到的目标点坐标 (to:(x,y) 中的x,y值)
// 在maixcam_parse_data函数中更新，可供其他函数（如判断函数）访问。
int target_x_coord = 0;
int target_y_coord = 0;

// 默认的激光坐标回调函数
// 当MaixCam解析出新的坐标数据时，如果没有设置其他自定义回调，将调用此函数。
static void default_laser_callback(LaserCoord_t coord)
{
    // 根据解析到的坐标类型进行处理
    if (coord.type == RED_LASER_ID) // 映射到 "to:(X,Y)" 数据，表示目标点坐标
    {
        // 第一次读取到坐标值（目标点），则初始化PID并设置目标
        // 注意：这里的app_pid_init()和app_pid_start()如果每收到一次目标点就调用，
        // 可能会导致PID频繁复位或重新启动。通常这些只在系统启动或目标首次确定时调用一次。
        // 建议添加一个标志位来确保只初始化一次。例如：
        // static bool pid_initialized = false;
        // if (!pid_initialized) {
        //     app_pid_init();
        //     app_pid_start();
        //     pid_initialized = true;
        // }

        // 如果app_pid_init是幂等操作（多次调用效果相同），或者你希望每次收到目标都重置PID，则保留。
        app_pid_init();
        // 将解析到的X,Y设置为PID控制器的目标位置
        app_pid_set_target(coord.x, coord.y);
        my_printf(&huart1, "Target: X=%d, Y=%d\r\n", coord.x, coord.y);
        // 如果app_pid_start是幂等操作，或者你希望每次收到目标都重置并启动PID，则保留。
        app_pid_start();
    }
    else if (coord.type == GREEN_LASER_ID) // 映射到 "pur:(X,Y)" 数据，表示实际激光点坐标
    {
        // 这个坐标通常是PID的当前反馈位置。
        my_printf(&huart1, "Laser: X=%d, Y=%d\r\n", coord.x, coord.y);
        // 如果你的PID系统需要实时更新当前激光位置，可以在这里调用相应的函数，例如：
        // app_pid_update_current_position(coord.x, coord.y);
    }
    else // 未知类型，或者用于调试
    {
        my_printf(&huart1, "Unknown type: %c, X=%d, Y=%d\r\n", coord.type, coord.x, coord.y);
    }
}

// 回调函数指针，默认指向默认回调函数 default_laser_callback
static LaserCoordCallback_t laser_coord_callback = default_laser_callback;

/**
 * @brief 设置激光坐标数据回调函数。
 *        用户可以注册自己的函数来处理MaixCam解析出的坐标数据。
 * @param callback 指向回调函数的指针。如果传入NULL，则恢复为默认回调函数。
 */
void maixcam_set_callback(LaserCoordCallback_t callback)
{
    if (callback != NULL)
        laser_coord_callback = callback;
    else
        laser_coord_callback = default_laser_callback;
}

/**
 * @brief MaixCam 数据解析函数。
 *        尝试解析特定格式的字符串（"to:(X,Y)" 或 "pur:(X,Y)"）。
 * @param buffer 待解析的字符串缓冲区。
 * @return 0: 成功解析并处理；
 *         -1: 缓冲区为空 (buffer为NULL)；
 *         -2: 未知格式或解析失败。
 */
int maixcam_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 缓冲区为空

    LaserCoord_t coord;
    int parsed_count;

    // 尝试解析 "to:(X,Y)" 格式
    // 检查字符串是否以 "to:(" 开头
    if (strncmp(buffer, "to:(", 4) == 0)
    {
        // 从 "to:(" 之后开始解析，期待 "%d,%d)" 的格式
        parsed_count = sscanf(buffer + 4, "%d,%d)", &coord.x, &coord.y);
        if (parsed_count == 2) // 成功解析出两个整数（X和Y）
        {
            coord.type = RED_LASER_ID; // 映射 "to" 数据为红色激光ID (目标点类型)

            // 更新全局变量中存储的目标点坐标
            target_x_coord = coord.x;
            target_y_coord = coord.y;

            // 调用已注册的回调函数处理解析结果
            if (laser_coord_callback)
                laser_coord_callback(coord);
            return 0; // 成功解析
        }
    }
    // 尝试解析 "pur:(X,Y)" 格式
    // 检查字符串是否以 "pur:(" 开头
    else if (strncmp(buffer, "pur:(", 5) == 0)
    {
        // 从 "pur:(" 之后开始解析，期待 "%d,%d)" 的格式
        parsed_count = sscanf(buffer + 5, "%d,%d)", &coord.x, &coord.y);
        if (parsed_count == 2) // 成功解析出两个整数（X和Y）
        {
            coord.type = GREEN_LASER_ID; // 映射 "pur" 数据为绿色激光ID (实际激光点类型)
            // 调用已注册的回调函数处理解析结果
            if (laser_coord_callback)
                laser_coord_callback(coord);
            return 0; // 成功解析
        }
    }

    // 如果不匹配任何已知格式
    return -2; // 未知格式或解析失败
}

/**
 * @brief MaixCam 数据解析任务函数。
 *        该函数通常在一个定时器或独立的任务中周期性调用，
 *        从环形缓冲区读取数据并进行解析。
 * @param timer 指向MultiTimer结构体的指针（如果使用MultiTimer库）。
 * @param userData 用户自定义数据指针。
 */
void maixcam_task(MultiTimer *timer, void *userData)
{
    // 假设 ringbuffer_cam 和 output_buffer_cam 已正确定义和初始化。
    // 例如：extern rt_ringbuffer_t ringbuffer_cam; extern uint8_t output_buffer_cam[BUFFER_SIZE];

    int length_cam = rt_ringbuffer_data_len(&ringbuffer_cam); // 获取环形缓冲区中待处理的数据长度
    if (length_cam > 0)
    {
        // 从环形缓冲区读取数据到临时缓冲区 output_buffer_cam。
        // 确保 output_buffer_cam 足够大以容纳最长的数据帧。
        rt_ringbuffer_get(&ringbuffer_cam, output_buffer_cam, length_cam);
        output_buffer_cam[length_cam] = '\0'; // 确保字符串以空字符结尾，便于sscanf等函数处理

        // 调用数据解析函数
        int result = maixcam_parse_data((char *)output_buffer_cam);
        if (result != 0) {
            // 可以根据result打印错误信息，用于调试。
            // my_printf(&huart1, "Parse error: %d, Data: %s\r\n", result, output_buffer_cam);
        }

        // 清空缓冲区内容（根据实际环形缓冲区的使用方式，这行可能不需要或有不同实现）。
        // 如果环形缓冲区机制会自动管理已读取数据，则可能不需要此清空。
        memset(output_buffer_cam, 0, length_cam);
    }
	maixcam_check_target_near_point();
    // multiTimerStart(&mt_pid, 10, app_pid_task, NULL); // 这行保持不变，PID通常在独立任务中运行。
}

// 辅助判断函数

/**
 * @brief 判断当前存储的目标点(target_x_coord, target_y_coord)是否在指定的矩形范围内。
 * @param min_x 矩形区域的最小X坐标。
 * @param max_x 矩形区域的最大X坐标。
 * @param min_y 矩形区域的最小Y坐标。
 * @param max_y 矩形区域的最大Y坐标。
 * @return 1 (true): 目标点在范围内；0 (false): 目标点不在范围内。
 */
int maixcam_is_target_in_range(int min_x, int max_x, int min_y, int max_y)
{
    return (target_x_coord >= min_x && target_x_coord <= max_x &&
            target_y_coord >= min_y && target_y_coord <= max_y) ? 1 : 0;
}

/**
 * @brief 计算当前存储的目标点(target_x_coord, target_y_coord)到指定中心点的欧几里得距离。
 * @param center_x 中心点的X坐标。
 * @param center_y 中心点的Y坐标。
 * @return 目标点到中心点的距离（整数部分）。
 *         需要包含 <math.h> 库。
 */
int maixcam_get_target_distance_from_center(int center_x, int center_y)
{
    int dx = target_x_coord - center_x;
    int dy = target_y_coord - center_y;
    // 使用sqrtf进行浮点计算，然后转换为int，可能会有精度损失。
    // 如果需要更精确的距离，可以返回float或double类型。
    return (int)sqrtf((float)dx * dx + (float)dy * dy); // 注意此处将dx*dx和dy*dy转换为float再计算，避免整数溢出
}

/**
 * @brief 判断目标是否接近指定点(160,120)
 */
void maixcam_check_target_near_point(void)
{
    int diff_x = abs(target_x_coord - 160);  // 与160的差值绝对值
    int diff_y = abs(target_y_coord - 120);  // 与120的差值绝对值

    if (diff_x < 15 && diff_y < 15) {
        // 当两个绝对值都小于15时的处理
        // TODO: 在这里添加具体指令
	app_laser_on();
    } else {
        // 否则的处理
        // TODO: 在这里添加具体指令
	app_laser_off();
    }
}
